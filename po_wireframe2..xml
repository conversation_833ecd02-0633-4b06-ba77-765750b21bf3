<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-07-31T00:00:00.000Z" agent="5.0" etag="xxx" version="21.6.5" type="device">
  <diagram name="Cogveel O2C Architecture" id="cogveel-arch">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Client Environment -->
        <mxCell id="client-env" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="40" y="40" width="300" height="280" as="geometry" />
        </mxCell>
        <mxCell id="client-title" value="CLIENT ENVIRONMENT" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="115" y="50" width="150" height="20" as="geometry" />
        </mxCell>
        
        <!-- Client Data Sources -->
        <mxCell id="erp-system" value="ERP System&#xa;(SAP/Oracle)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="60" y="80" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="mes-system" value="MES System&#xa;(Manufacturing)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="160" y="80" width="80" height="50" as="geometry" />
        </mxCell>
        <mxCell id="contracts-db" value="Contracts&#xa;Database" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="260" y="80" width="70" height="50" as="geometry" />
        </mxCell>
        
        <!-- Edge Processing -->
        <mxCell id="edge-container" value="EDGE PROCESSING UNIT" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="60" y="150" width="260" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="data-collector" value="Data Collector&#xa;&amp; Validator" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="70" y="180" width="70" height="40" as="geometry" />
        </mxCell>
        <mxCell id="local-processor" value="Local ML&#xa;Processor" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="150" y="180" width="70" height="40" as="geometry" />
        </mxCell>
        <mxCell id="anonymizer" value="Data&#xa;Anonymizer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="230" y="180" width="70" height="40" as="geometry" />
        </mxCell>
        
        <!-- Secure API Gateway -->
        <mxCell id="secure-tunnel" value="SECURE API TUNNEL" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f0f0f0;strokeColor=#666666;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="60" y="240" width="260" height="30" as="geometry" />
        </mxCell>
        
        <!-- Cogveel Cloud Platform -->
        <mxCell id="cloud-platform" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="400" y="40" width="720" height="460" as="geometry" />
        </mxCell>
        <mxCell id="platform-title" value="COGVEEL CLOUD PLATFORM" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="680" y="50" width="200" height="20" as="geometry" />
        </mxCell>
        
        <!-- Data Readiness Assessment -->
        <mxCell id="assessment-engine" value="DATA READINESS ASSESSMENT ENGINE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="420" y="80" width="320" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="quality-scorer" value="Data Quality&#xa;Scorer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="430" y="130" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="schema-mapper" value="Schema&#xa;Mapper" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="520" y="130" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="roi-calculator" value="ROI&#xa;Calculator" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="610" y="130" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="gap-analyzer" value="Gap&#xa;Analyzer" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="700" y="130" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- Synthetic Data Generator -->
        <mxCell id="synthetic-engine" value="SYNTHETIC DATA GENERATOR" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="800" y="80" width="300" height="40" as="geometry" />
        </mxCell>
        
        <mxCell id="bom-generator" value="BOM Structure&#xa;Generator" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="810" y="130" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="oee-simulator" value="OEE&#xa;Simulator" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="900" y="130" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="contract-templates" value="Contract&#xa;Templates" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="990" y="130" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- Core Analytics Engine -->
        <mxCell id="analytics-core" value="CORE ANALYTICS ENGINE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="420" y="200" width="680" height="40" as="geometry" />
        </mxCell>
        
        <!-- Analytics Modules -->
        <mxCell id="bom-analyzer" value="BOM DISCREPANCY&#xa;ANALYZER" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="430" y="260" width="130" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="contract-intel" value="CONTRACT&#xa;INTELLIGENCE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="580" y="260" width="130" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="oee-monitor" value="OEE REAL-TIME&#xa;MONITOR" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="730" y="260" width="130" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="data-corrector" value="DATA QUALITY&#xa;CORRECTOR" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="880" y="260" width="130" height="60" as="geometry" />
        </mxCell>
        
        <!-- Security Layer -->
        <mxCell id="security-layer" value="SECURITY &amp; COMPLIANCE LAYER" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="420" y="340" width="680" height="30" as="geometry" />
        </mxCell>
        
        <mxCell id="zero-trust" value="Zero Trust&#xa;Architecture" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="430" y="380" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="anonymization" value="Data&#xa;Anonymization" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="520" y="380" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="audit-logs" value="Audit&#xa;Logging" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="610" y="380" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="compliance" value="SOC2/ISO&#xa;Compliance" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="700" y="380" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="encryption" value="End-to-End&#xa;Encryption" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="790" y="380" width="80" height="40" as="geometry" />
        </mxCell>
        <mxCell id="rbac" value="Role-Based&#xa;Access" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="880" y="380" width="80" height="40" as="geometry" />
        </mxCell>
        
        <!-- Client Portal -->
        <mxCell id="client-portal" value="CLIENT PORTAL &amp; DASHBOARD" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="420" y="440" width="680" height="40" as="geometry" />
        </mxCell>
        
        <!-- Portal Components -->
        <mxCell id="demo-env" value="Demo Environment&#xa;(Synthetic Data)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="40" y="550" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="onboarding" value="Gradual Data&#xa;Onboarding" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="180" y="550" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="progress-tracker" value="Trust Building&#xa;Progress Tracker" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="320" y="550" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="roi-dashboard" value="Real-time ROI&#xa;Dashboard" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="460" y="550" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="insights-reports" value="Automated&#xa;Insights Reports" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="600" y="550" width="120" height="50" as="geometry" />
        </mxCell>
        
        <mxCell id="alert-system" value="Exception&#xa;Alert System" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="740" y="550" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- Data Flow Arrows -->
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#d79b00;" edge="1" parent="1" source="erp-system" target="data-collector">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="100" y="140" as="sourcePoint" />
            <mxPoint x="105" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#d79b00;" edge="1" parent="1" source="mes-system" target="local-processor">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="200" y="140" as="sourcePoint" />
            <mxPoint x="185" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#d79b00;" edge="1" parent="1" source="contracts-db" target="anonymizer">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="295" y="140" as="sourcePoint" />
            <mxPoint x="265" y="180" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow4" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=3;strokeColor=#82b366;" edge="1" parent="1" source="secure-tunnel" target="assessment-engine">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="340" y="255" as="sourcePoint" />
            <mxPoint x="420" y="100" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="arrow5" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="client-portal" target="demo-env">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="420" y="480" as="sourcePoint" />
            <mxPoint x="100" y="550" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Labels for Implementation Phases -->
        <mxCell id="phase1" value="PHASE 1: TRUST BUILDING" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=3;fontColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="40" y="630" width="200" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="phase2" value="PHASE 2: DATA ASSESSMENT" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=3;fontColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="420" y="630" width="200" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="phase3" value="PHASE 3: GRADUAL ONBOARDING" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=3;fontColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="800" y="630" width="200" height="20" as="geometry" />
        </mxCell>
        
        <!-- Key Features Text -->
        <mxCell id="features-title" value="KEY ARCHITECTURAL FEATURES:" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="40" y="670" width="200" height="20" as="geometry" />
        </mxCell>
        
        <mxCell id="features-list" value="• Edge processing keeps sensitive data on-premise&#xa;• Synthetic data enables risk-free demonstrations&#xa;• Modular deployment allows gradual trust building&#xa;• API-first design ensures data sovereignty&#xa;• Zero-trust security with full audit trails&#xa;• ROI-driven onboarding with measurable outcomes" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="40" y="690" width="1060" height="80" as="geometry" />
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>