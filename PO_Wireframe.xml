<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="28.0.7">
  <diagram name="CogVeel AI Platform Wireframe" id="cogveel-wireframe">
    <mxGraphModel dx="1464" dy="607" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1600" pageHeight="2400" math="0" shadow="1">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="header-bg" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2c3e50;strokeColor=#1a252f;strokeWidth=3;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="50" y="20" width="1500" height="100" as="geometry" />
        </mxCell>
        <mxCell id="main-title" value="&lt;font color=&quot;#ffffff&quot; size=&quot;6&quot;&gt;&lt;b&gt;CogVeel AI Platform - System Wireframe&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="300" y="40" width="1000" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sub-title" value="&lt;font color=&quot;#ecf0f1&quot; size=&quot;3&quot;&gt;Complete O2C Agentic AI Solution Architecture&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="500" y="70" width="600" height="20" as="geometry" />
        </mxCell>
        <mxCell id="version" value="&lt;font color=&quot;#bdc3c7&quot; size=&quot;2&quot;&gt;Version 2.0 | Manufacturing Intelligence Platform&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="600" y="90" width="400" height="20" as="geometry" />
        </mxCell>
        <mxCell id="dashboard-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#2c3e50;strokeWidth=3;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="50" y="140" width="1500" height="900" as="geometry" />
        </mxCell>
        <mxCell id="dashboard-header" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2c3e50;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="50" y="140" width="1500" height="50" as="geometry" />
        </mxCell>
        <mxCell id="dashboard-title" value="&lt;font color=&quot;#ffffff&quot; size=&quot;4&quot;&gt;&lt;b&gt;🏠 MAIN DASHBOARD - COMMAND CENTER&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="70" y="155" width="400" height="20" as="geometry" />
        </mxCell>
        <mxCell id="nav-header" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#dee2e6;" parent="1" vertex="1">
          <mxGeometry x="70" y="210" width="1460" height="60" as="geometry" />
        </mxCell>
        <mxCell id="logo" value="&lt;b&gt;CogVeel AI&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#2c3e50;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="90" y="225" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="search-bar" value="🔍 Search orders, customers, products..." style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#ced4da;fontColor=#6c757d;" parent="1" vertex="1">
          <mxGeometry x="600" y="225" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="notifications" value="&lt;font color=&quot;#dc3545&quot;&gt;&lt;b&gt;3&lt;/b&gt;&lt;/font&gt;&lt;br&gt;Alerts" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#28a745;" parent="1" vertex="1">
          <mxGeometry x="1300" y="225" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="user-profile" value="John Doe" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#2c3e50;" parent="1" vertex="1">
          <mxGeometry x="1380" y="225" width="80" height="30" as="geometry" />
        </mxCell>
        <mxCell id="sidebar" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#dee2e6;" parent="1" vertex="1">
          <mxGeometry x="70" y="290" width="220" height="730" as="geometry" />
        </mxCell>
        <mxCell id="nav-title" value="&lt;b&gt;📱 NAVIGATION MENU&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e9ecef;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="70" y="290" width="220" height="40" as="geometry" />
        </mxCell>
        <mxCell id="nav-dashboard" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;📊 Dashboard&lt;/b&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#3498db;strokeColor=#2980b9;" parent="1" vertex="1">
          <mxGeometry x="85" y="350" width="190" height="35" as="geometry" />
        </mxCell>
        <mxCell id="nav-orders" value="📄 Order Processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#dee2e6;" parent="1" vertex="1">
          <mxGeometry x="85" y="395" width="190" height="35" as="geometry" />
        </mxCell>
        <mxCell id="order-badge" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;47&lt;/b&gt;&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dc3545;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="250" y="405" width="20" height="15" as="geometry" />
        </mxCell>
        <mxCell id="nav-agents" value="🤖 Agent Management" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#dee2e6;" parent="1" vertex="1">
          <mxGeometry x="85" y="440" width="190" height="35" as="geometry" />
        </mxCell>
        <mxCell id="agent-badge" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;12&lt;/b&gt;&lt;/font&gt;" style="ellipse;whiteSpace=wrap;html=1;fillColor=#17a2b8;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="250" y="450" width="20" height="15" as="geometry" />
        </mxCell>
        <mxCell id="nav-data" value="🔧 Data Preparation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#dee2e6;" parent="1" vertex="1">
          <mxGeometry x="85" y="485" width="190" height="35" as="geometry" />
        </mxCell>
        <mxCell id="separator" value="" style="endArrow=none;html=1;strokeColor=#dee2e6;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="85" y="535" as="sourcePoint" />
            <mxPoint x="275" y="535" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="specialized-label" value="&lt;font color=&quot;#6c757d&quot;&gt;&lt;b&gt;SPECIALIZED MODULES&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="90" y="540" width="180" height="20" as="geometry" />
        </mxCell>
        <mxCell id="nav-contracts" value="📋 Contract Intelligence" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#dee2e6;" parent="1" vertex="1">
          <mxGeometry x="85" y="560" width="190" height="35" as="geometry" />
        </mxCell>
        <mxCell id="nav-bom" value="🔍 BOM Validation" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#dee2e6;" parent="1" vertex="1">
          <mxGeometry x="85" y="605" width="190" height="35" as="geometry" />
        </mxCell>
        <mxCell id="nav-oee" value="⚡ OEE Monitoring" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#dee2e6;" parent="1" vertex="1">
          <mxGeometry x="85" y="650" width="190" height="35" as="geometry" />
        </mxCell>
        <mxCell id="nav-analytics" value="📈 Advanced Analytics" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#dee2e6;" parent="1" vertex="1">
          <mxGeometry x="85" y="695" width="190" height="35" as="geometry" />
        </mxCell>
        <mxCell id="quick-actions" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e7f3ff;strokeColor=#3498db;" parent="1" vertex="1">
          <mxGeometry x="85" y="750" width="190" height="80" as="geometry" />
        </mxCell>
        <mxCell id="quick-title" value="&lt;b&gt;⚡ QUICK ACTIONS&lt;/b&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="95" y="760" width="120" height="20" as="geometry" />
        </mxCell>
        <mxCell id="new-order-btn" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;New Order&lt;/b&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#3498db;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="95" y="780" width="80" height="20" as="geometry" />
        </mxCell>
        <mxCell id="run-report-btn" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;Run Report&lt;/b&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#28a745;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="185" y="780" width="80" height="20" as="geometry" />
        </mxCell>
        <mxCell id="emergency-btn" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;Emergency Override&lt;/b&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6c757d;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="95" y="805" width="170" height="20" as="geometry" />
        </mxCell>
        <mxCell id="main-content" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#dee2e6;" parent="1" vertex="1">
          <mxGeometry x="310" y="280" width="930" height="740" as="geometry" />
        </mxCell>
        <mxCell id="kpi-title" value="&lt;b&gt;📊 REAL-TIME PERFORMANCE DASHBOARD&lt;/b&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="330" y="310" width="400" height="20" as="geometry" />
        </mxCell>
        <mxCell id="last-updated" value="&lt;font color=&quot;#6c757d&quot;&gt;Last updated: 2 seconds ago&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1020" y="308" width="140" height="20" as="geometry" />
        </mxCell>
        <mxCell id="kpi-orders" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#2196f3;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="330" y="335" width="210" height="90" as="geometry" />
        </mxCell>
        <mxCell id="orders-value" value="&lt;font size=&quot;6&quot; color=&quot;#1565c0&quot;&gt;&lt;b&gt;2,847&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="320" y="365" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="orders-label" value="Orders Processed Today" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="325" y="395" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="orders-trend" value="&lt;font color=&quot;#28a745&quot;&gt;▲ +12% vs yesterday&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="370" y="345" width="120" height="15" as="geometry" />
        </mxCell>
        <mxCell id="kpi-accuracy" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="560" y="335" width="210" height="90" as="geometry" />
        </mxCell>
        <mxCell id="accuracy-value" value="&lt;font size=&quot;6&quot; color=&quot;#2e7d32&quot;&gt;&lt;b&gt;99.2%&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="560" y="365" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="accuracy-label" value="AI Accuracy Rate" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="560" y="395" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="accuracy-trend" value="&lt;font color=&quot;#28a745&quot;&gt;▲ +0.3% this week&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="605" y="345" width="120" height="15" as="geometry" />
        </mxCell>
        <mxCell id="kpi-savings" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ff9800;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="790" y="335" width="210" height="90" as="geometry" />
        </mxCell>
        <mxCell id="savings-value" value="&lt;font size=&quot;6&quot; color=&quot;#ef6c00&quot;&gt;&lt;b&gt;$47K&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="790" y="365" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="savings-label" value="Cost Savings This Month" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="790" y="395" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="savings-trend" value="&lt;font color=&quot;#28a745&quot;&gt;▲ +$4.2K this week&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="835" y="345" width="120" height="15" as="geometry" />
        </mxCell>
        <mxCell id="kpi-time" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f3e5f5;strokeColor=#9c27b0;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="1020" y="335" width="210" height="90" as="geometry" />
        </mxCell>
        <mxCell id="time-value" value="&lt;font size=&quot;6&quot; color=&quot;#7b1fa2&quot;&gt;&lt;b&gt;7.3 min&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1020" y="365" width="210" height="30" as="geometry" />
        </mxCell>
        <mxCell id="time-label" value="Avg Processing Time" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1020" y="395" width="210" height="20" as="geometry" />
        </mxCell>
        <mxCell id="time-trend" value="&lt;font color=&quot;#28a745&quot;&gt;▼ -1.2 min improved&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1055" y="345" width="140" height="15" as="geometry" />
        </mxCell>
        <mxCell id="workflow-area" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#dee2e6;strokeWidth=1;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="330" y="450" width="900" height="350" as="geometry" />
        </mxCell>
        <mxCell id="workflow-title" value="&lt;b&gt;🔄 LIVE WORKFLOW ORCHESTRATION&lt;/b&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="340" y="465" width="300" height="20" as="geometry" />
        </mxCell>
        <mxCell id="realtime-label" value="&lt;font color=&quot;#6c757d&quot;&gt;Real-time processing view&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1090" y="465" width="130" height="20" as="geometry" />
        </mxCell>
        <mxCell id="workflow-capture" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;📥 Order Capture&lt;/b&gt;&lt;br&gt;Processing: 47 orders&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dc3545;strokeColor=#c82333;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="360" y="500" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="workflow-validation" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;🛡️ AI Validation&lt;/b&gt;&lt;br&gt;Accuracy: 99.2%&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fd7e14;strokeColor=#e65100;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="550" y="500" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="workflow-processing" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;⚡ Smart Processing&lt;/b&gt;&lt;br&gt;Avg: 2.1 min&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#28a745;strokeColor=#1e7e34;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="740" y="500" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="workflow-approval" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;✅ Final Approval&lt;/b&gt;&lt;br&gt;Queue: 12 pending&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6f42c1;strokeColor=#5a32a3;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="930" y="500" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="workflow-exception" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;🚨 Exception Handling&lt;/b&gt;&lt;br&gt;Active: 3 escalations&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#17a2b8;strokeColor=#0056b3;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="540" y="620" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="arrow1" value="" style="endArrow=classic;html=1;strokeColor=#2c3e50;strokeWidth=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="510" y="525" as="sourcePoint" />
            <mxPoint x="540" y="525" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow2" value="" style="endArrow=classic;html=1;strokeColor=#2c3e50;strokeWidth=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="700" y="525" as="sourcePoint" />
            <mxPoint x="730" y="525" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow3" value="" style="endArrow=classic;html=1;strokeColor=#2c3e50;strokeWidth=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="890" y="525" as="sourcePoint" />
            <mxPoint x="920" y="525" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow4" value="" style="endArrow=classic;html=1;strokeColor=#2c3e50;strokeWidth=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="620" y="560" as="sourcePoint" />
            <mxPoint x="620" y="610" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow5" value="" style="endArrow=classic;html=1;strokeColor=#2c3e50;strokeWidth=3;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="810" y="560" as="sourcePoint" />
            <mxPoint x="700" y="610" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="current-order" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e7f3ff;strokeColor=#3498db;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="330" y="720" width="900" height="120" as="geometry" />
        </mxCell>
        <mxCell id="order-spotlight-title" value="&lt;font color=&quot;#1565c0&quot;&gt;&lt;b&gt;🎯 ACTIVE ORDER SPOTLIGHT&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="350" y="735" width="300" height="20" as="geometry" />
        </mxCell>
        <mxCell id="order-details" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#dee2e6;" parent="1" vertex="1">
          <mxGeometry x="350" y="755" width="850" height="70" as="geometry" />
        </mxCell>
        <mxCell id="order-info" value="&lt;b&gt;Order #SO-2024-5847 - TechManufacturing Corp (Priority: HIGH)&lt;/b&gt;&lt;br&gt;📦 Products: 500x Blue Widgets (SKU: BW-2024-X) | 💰 Value: $47,500 | 👤 Customer: AAA Credit Rating&lt;br&gt;🤖 AI Decisions: Applied 15% volume discount | Reserved inventory from Warehouse B | Expedited shipping" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="360" y="765" width="830" height="50" as="geometry" />
        </mxCell>
        <mxCell id="agent-sidebar" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2c3e50;strokeColor=#1a252f;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="1250" y="290" width="280" height="730" as="geometry" />
        </mxCell>
        <mxCell id="agent-header" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#34495e;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="1250" y="290" width="280" height="45" as="geometry" />
        </mxCell>
        <mxCell id="agent-title" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;AI AGENT CONTROL CENTER&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1260" y="308" width="180" height="20" as="geometry" />
        </mxCell>
        <mxCell id="system-health" value="&lt;font color=&quot;#ffffff&quot;&gt;● System Healthy&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=right;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1430" y="290" width="100" height="20" as="geometry" />
        </mxCell>
        <mxCell id="agent1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;fillOpacity=10;strokeColor=#546e7a;" parent="1" vertex="1">
          <mxGeometry x="1270" y="350" width="240" height="70" as="geometry" />
        </mxCell>
        <mxCell id="agent1-info" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;Order Intelligence Agent&lt;/b&gt;&lt;br&gt;Status: Processing 47 orders&lt;br&gt;Performance: 99.1% success rate&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1310" y="365" width="190" height="40" as="geometry" />
        </mxCell>
        <mxCell id="agent1-status" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#28a745;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="1282" y="362" width="16" height="16" as="geometry" />
        </mxCell>
        <mxCell id="agent2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;fillOpacity=10;strokeColor=#546e7a;" parent="1" vertex="1">
          <mxGeometry x="1270" y="435" width="240" height="70" as="geometry" />
        </mxCell>
        <mxCell id="agent2-info" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;Credit Guardian Agent&lt;/b&gt;&lt;br&gt;Status: Validating ABC Corp&lt;br&gt;Queue: 12 credit checks pending&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1310" y="450" width="190" height="40" as="geometry" />
        </mxCell>
        <mxCell id="agent2-status" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#ffc107;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="1282" y="447" width="16" height="16" as="geometry" />
        </mxCell>
        <mxCell id="agent3" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;fillOpacity=10;strokeColor=#546e7a;" parent="1" vertex="1">
          <mxGeometry x="1270" y="520" width="240" height="70" as="geometry" />
        </mxCell>
        <mxCell id="agent3-info" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;Inventory Orchestrator&lt;/b&gt;&lt;br&gt;Status: Optimizing fulfillment&lt;br&gt;Multi-location: 4 warehouses&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1310" y="535" width="190" height="40" as="geometry" />
        </mxCell>
        <mxCell id="agent3-status" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#28a745;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="1282" y="532" width="16" height="16" as="geometry" />
        </mxCell>
        <mxCell id="agent4" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;fillOpacity=10;strokeColor=#546e7a;" parent="1" vertex="1">
          <mxGeometry x="1270" y="605" width="240" height="70" as="geometry" />
        </mxCell>
        <mxCell id="agent4-info" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;Exception Handler&lt;/b&gt;&lt;br&gt;Status: Resolving capacity conflict&lt;br&gt;Priority: 3 high-severity issues&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1310" y="620" width="190" height="40" as="geometry" />
        </mxCell>
        <mxCell id="agent4-status" value="" style="ellipse;whiteSpace=wrap;html=1;fillColor=#dc3545;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="1282" y="617" width="16" height="16" as="geometry" />
        </mxCell>
        <mxCell id="performance-metrics" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;fillOpacity=5;strokeColor=#546e7a;" parent="1" vertex="1">
          <mxGeometry x="1270" y="690" width="240" height="140" as="geometry" />
        </mxCell>
        <mxCell id="metrics-title" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;📊 SYSTEM PERFORMANCE&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1280" y="700" width="200" height="20" as="geometry" />
        </mxCell>
        <mxCell id="metrics-details" value="&lt;font color=&quot;#ecf0f1&quot;&gt;Agents Active: 12/15&lt;br&gt;Response Time: 1.2s avg&lt;br&gt;Success Rate: 99.2%&lt;br&gt;Throughput: 847/hour&lt;br&gt;System Load: 67%&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1280" y="730" width="200" height="90" as="geometry" />
        </mxCell>
        <mxCell id="data-prep-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#2c3e50;strokeWidth=3;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="50" y="1070" width="1500" height="450" as="geometry" />
        </mxCell>
        <mxCell id="data-prep-header" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#2c3e50;strokeColor=none;" parent="1" vertex="1">
          <mxGeometry x="50" y="1070" width="1500" height="50" as="geometry" />
        </mxCell>
        <mxCell id="data-prep-title" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;🔧 DATA PREPARATION HUB - CONTRACT-DRIVEN INTELLIGENCE&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="70" y="1092" width="600" height="20" as="geometry" />
        </mxCell>
        <mxCell id="stage1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3cd;strokeColor=#f39c12;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="80" y="1140" width="350" height="180" as="geometry" />
        </mxCell>
        <mxCell id="stage1-title" value="&lt;font color=&quot;#856404&quot;&gt;&lt;b&gt;📄 STAGE 1: CONTRACT UPLOAD&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="80" y="1155" width="350" height="25" as="geometry" />
        </mxCell>
        <mxCell id="upload-zone" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#f39c12;strokeWidth=2;dashed=1;fillOpacity=80;" parent="1" vertex="1">
          <mxGeometry x="100" y="1180" width="310" height="80" as="geometry" />
        </mxCell>
        <mxCell id="upload-text" value="📁 Intelligent Document Intake&lt;br&gt;Multi-format Support: PDFs, Emails, Scanned Documents&lt;br&gt;AI-Powered OCR | NLP Entity Recognition&lt;br&gt;Real-time Processing Pipeline" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="110" y="1190" width="290" height="60" as="geometry" />
        </mxCell>
        <mxCell id="stage1-status" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;✅ 47 Data Points Extracted&lt;/b&gt;&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#28a745;strokeColor=#1e7e34;" parent="1" vertex="1">
          <mxGeometry x="320" y="1275" width="100" height="25" as="geometry" />
        </mxCell>
        <mxCell id="flow-arrow1" value="Real-time" style="endArrow=classic;html=1;strokeColor=#2c3e50;strokeWidth=4;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="440" y="1230" as="sourcePoint" />
            <mxPoint x="480" y="1230" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="stage2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d1ecf1;strokeColor=#17a2b8;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="490" y="1140" width="350" height="180" as="geometry" />
        </mxCell>
        <mxCell id="stage2-title" value="&lt;font color=&quot;#0c5460&quot;&gt;&lt;b&gt;🧠 STAGE 2: AI PROCESSING&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="490" y="1155" width="350" height="25" as="geometry" />
        </mxCell>
        <mxCell id="processing-steps" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#17a2b8;fillOpacity=90;" parent="1" vertex="1">
          <mxGeometry x="510" y="1180" width="310" height="130" as="geometry" />
        </mxCell>
        <mxCell id="processing-text" value="&lt;b&gt;🔍 Intelligent Analysis Pipeline:&lt;/b&gt;&lt;br&gt;• OCR &amp; Document Structure Recognition&lt;br&gt;• NLP Entity Extraction &amp; Classification&lt;br&gt;• Smart Validation Against Business Rules&lt;br&gt;• External Data Fusion &amp; Enrichment&lt;br&gt;• Predictive Data Completion&lt;br&gt;• Collaborative Stakeholder Validation" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="520" y="1190" width="290" height="110" as="geometry" />
        </mxCell>
        <mxCell id="flow-arrow2" value="Validated" style="endArrow=classic;html=1;strokeColor=#2c3e50;strokeWidth=4;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="850" y="1230" as="sourcePoint" />
            <mxPoint x="890" y="1230" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="stage3" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d4edda;strokeColor=#28a745;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="900" y="1140" width="350" height="180" as="geometry" />
        </mxCell>
        <mxCell id="stage3-title" value="&lt;font color=&quot;#155724&quot;&gt;&lt;b&gt;📊 STAGE 3: MASTER DATA&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="900" y="1155" width="350" height="25" as="geometry" />
        </mxCell>
        <mxCell id="master-data-outputs" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#28a745;fillOpacity=90;" parent="1" vertex="1">
          <mxGeometry x="920" y="1180" width="310" height="130" as="geometry" />
        </mxCell>
        <mxCell id="master-data-text" value="&lt;b&gt;🎯 Enriched Business Assets:&lt;/b&gt;&lt;br&gt;• Validated Vendor Master Records&lt;br&gt;• Contract Terms &amp; Compliance Mapping&lt;br&gt;• Reusable Template Generation&lt;br&gt;• Auto-sync to ERP/CRM Systems&lt;br&gt;• Audit Trail &amp; Compliance Records&lt;br&gt;• Predictive Analytics Ready" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="930" y="1190" width="290" height="110" as="geometry" />
        </mxCell>
        <mxCell id="quality-center" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#6c757d;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="1270" y="1140" width="260" height="180" as="geometry" />
        </mxCell>
        <mxCell id="quality-title" value="&lt;font color=&quot;#495057&quot;&gt;&lt;b&gt;📈 QUALITY CONTROL CENTER&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1270" y="1155" width="260" height="25" as="geometry" />
        </mxCell>
        <mxCell id="quality-accuracy" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;98.7%&lt;/b&gt;&lt;br&gt;Data Accuracy&lt;br&gt;↗️ +0.3%&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#28a745;strokeColor=#1e7e34;" parent="1" vertex="1">
          <mxGeometry x="1290" y="1180" width="90" height="50" as="geometry" />
        </mxCell>
        <mxCell id="quality-records" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;847&lt;/b&gt;&lt;br&gt;Records/Day&lt;br&gt;↗️ +12%&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#17a2b8;strokeColor=#117a8b;" parent="1" vertex="1">
          <mxGeometry x="1390" y="1180" width="90" height="50" as="geometry" />
        </mxCell>
        <mxCell id="quality-pending" value="&lt;b&gt;23&lt;/b&gt;&lt;br&gt;Pending Review&lt;br&gt;↘️ -5" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffc107;strokeColor=#e0a800;" parent="1" vertex="1">
          <mxGeometry x="1290" y="1240" width="90" height="50" as="geometry" />
        </mxCell>
        <mxCell id="quality-time" value="&lt;font color=&quot;#ffffff&quot;&gt;&lt;b&gt;2.1s&lt;/b&gt;&lt;br&gt;Avg Process Time&lt;br&gt;↘️ -0.3s&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#6f42c1;strokeColor=#59359a;" parent="1" vertex="1">
          <mxGeometry x="1390" y="1240" width="90" height="50" as="geometry" />
        </mxCell>
        <mxCell id="feature1" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#2196f3;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="80" y="1350" width="440" height="140" as="geometry" />
        </mxCell>
        <mxCell id="feature1-title" value="&lt;font color=&quot;#0d47a1&quot;&gt;&lt;b&gt;🎯 SMART VALIDATION ENGINE&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="80" y="1365" width="440" height="25" as="geometry" />
        </mxCell>
        <mxCell id="feature1-text" value="• Multi-stakeholder validation workflows with role-based routing&lt;br&gt;• Real-time accuracy scoring with confidence indicators&lt;br&gt;• Automated correction suggestions based on historical patterns&lt;br&gt;• Consensus-based approval mechanisms with audit trails&lt;br&gt;• Integration with compliance frameworks and regulatory standards" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="100" y="1390" width="410" height="80" as="geometry" />
        </mxCell>
        <mxCell id="feature2" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="540" y="1350" width="440" height="140" as="geometry" />
        </mxCell>
        <mxCell id="feature2-title" value="&lt;font color=&quot;#1b5e20&quot;&gt;&lt;b&gt;🔮 PREDICTIVE COMPLETION AI&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="540" y="1365" width="440" height="25" as="geometry" />
        </mxCell>
        <mxCell id="feature2-text" value="• ML-powered data suggestions based on document context and history&lt;br&gt;• Industry-specific pattern recognition and benchmarking&lt;br&gt;• Context-aware template generation with smart defaults&lt;br&gt;• Continuous learning from user corrections and feedback&lt;br&gt;• Confidence scoring for predicted values with uncertainty quantification" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="560" y="1390" width="400" height="80" as="geometry" />
        </mxCell>
        <mxCell id="feature3" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ff9800;strokeWidth=2;shadow=1;" parent="1" vertex="1">
          <mxGeometry x="1000" y="1350" width="530" height="140" as="geometry" />
        </mxCell>
        <mxCell id="feature3-title" value="&lt;font color=&quot;#e65100&quot;&gt;&lt;b&gt;🌐 EXTERNAL DATA FUSION PLATFORM&lt;/b&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1000" y="1365" width="530" height="25" as="geometry" />
        </mxCell>
        <mxCell id="feature3-text" value="• Real-time integration with D&amp;B, Experian, and credit rating agencies&lt;br&gt;• Regulatory database monitoring (OFAC, sanctions lists, compliance)&lt;br&gt;• Industry-specific data sources and market intelligence feeds&lt;br&gt;• Automated risk assessment with financial health indicators&lt;br&gt;• Global address validation and tax jurisdiction identification" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="1020" y="1390" width="490" height="80" as="geometry" />
        </mxCell>
        <mxCell id="footer" value="&lt;font color=&quot;#6c757d&quot;&gt;&lt;i&gt;CogVeel AI Platform Wireframe v2.0 | Generated for Draw.io | Manufacturing Intelligence Solution&lt;/i&gt;&lt;/font&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="200" y="1550" width="1200" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
