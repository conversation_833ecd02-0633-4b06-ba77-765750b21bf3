<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="28.0.7">
  <diagram name="Manufacturing Dashboard Wireframe" id="wireframe-main">
    <mxGraphModel dx="1398" dy="575" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="login-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#dee2e6;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="50" y="50" width="300" height="400" as="geometry" />
        </mxCell>
        <mxCell id="login-title" value="Manufacturing Intelligence Platform" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="60" y="80" width="280" height="30" as="geometry" />
        </mxCell>
        <mxCell id="login-form" value="Username&#xa;&#xa;Password&#xa;&#xa;[Login Button]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=20;spacingTop=20;" parent="1" vertex="1">
          <mxGeometry x="80" y="150" width="240" height="150" as="geometry" />
        </mxCell>
        <mxCell id="dashboard-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#dee2e6;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="400" y="50" width="700" height="500" as="geometry" />
        </mxCell>
        <mxCell id="dashboard-header" value="Manufacturing Intelligence Dashboard" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#007bff;fontColor=#ffffff;fontSize=18;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="400" y="50" width="700" height="50" as="geometry" />
        </mxCell>
        <mxCell id="breadcrumb" value="Home &gt; Dashboard" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#6c757d;" parent="1" vertex="1">
          <mxGeometry x="420" y="110" width="200" height="20" as="geometry" />
        </mxCell>
        <mxCell id="card-data-health" value="Data Health&#xa;&#xa;Data Quality: 87%&#xa;Issues Found: 12&#xa;&#xa;[View Details]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;align=center;verticalAlign=top;spacingTop=15;" parent="1" vertex="1">
          <mxGeometry x="430" y="150" width="150" height="120" as="geometry" />
        </mxCell>
        <mxCell id="card-purchasing" value="Purchasing&#xa;&#xa;Active Contracts: 45&#xa;Pending POs: 23&#xa;&#xa;[View Details]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;align=center;verticalAlign=top;spacingTop=15;" parent="1" vertex="1">
          <mxGeometry x="600" y="150" width="150" height="120" as="geometry" />
        </mxCell>
        <mxCell id="card-bom" value="BOM Review&#xa;&#xa;Discrepancies: 8&#xa;Pending Reviews: 5&#xa;&#xa;[View Details]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ff9800;align=center;verticalAlign=top;spacingTop=15;" parent="1" vertex="1">
          <mxGeometry x="430" y="290" width="150" height="120" as="geometry" />
        </mxCell>
        <mxCell id="card-oee" value="OEE Monitoring&#xa;&#xa;Current OEE: 78%&#xa;Active Alerts: 3&#xa;&#xa;[View Details]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#e91e63;align=center;verticalAlign=top;spacingTop=15;" parent="1" vertex="1">
          <mxGeometry x="600" y="290" width="150" height="120" as="geometry" />
        </mxCell>
        <mxCell id="notifications" value="Recent Activity&#xa;&#xa;• Data fix applied to vendor records&#xa;• PO-001234 flagged for review&#xa;• BOM v2.1 approved&#xa;• OEE alert: Line 3 downtime" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=10;spacingTop=10;" parent="1" vertex="1">
          <mxGeometry x="770" y="150" width="300" height="260" as="geometry" />
        </mxCell>
        <mxCell id="data-health-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#dee2e6;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="50" y="580" width="1050" height="600" as="geometry" />
        </mxCell>
        <mxCell id="data-health-header" value="Data Health Module" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#1976d2;fontColor=#ffffff;fontSize=18;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="50" y="580" width="1050" height="50" as="geometry" />
        </mxCell>
        <mxCell id="data-breadcrumb" value="Home &gt; Dashboard &gt; Data Health" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#6c757d;" parent="1" vertex="1">
          <mxGeometry x="70" y="640" width="300" height="20" as="geometry" />
        </mxCell>
        <mxCell id="upload-section" value="Data Source Connection&#xa;&#xa;[Upload File] [Connect ERP] [API Integration]&#xa;&#xa;Status: Connected to ERP System&#xa;Last Sync: 2 hours ago" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;" parent="1" vertex="1">
          <mxGeometry x="70" y="670" width="400" height="100" as="geometry" />
        </mxCell>
        <mxCell id="profiling-results" value="Data Profiling Results&#xa;&#xa;Completeness: 87% (2,145 missing values)&#xa;Duplicates: 34 records found&#xa;Format Issues: 156 date format errors&#xa;&#xa;[View Detailed Report]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;" parent="1" vertex="1">
          <mxGeometry x="490" y="670" width="400" height="100" as="geometry" />
        </mxCell>
        <mxCell id="data-table" value="Data Records with Issues&#xa;&#xa;ID | Vendor | Date | Issue&#xa;001 | ABC Corp | [missing] | Missing Date&#xa;002 | XYZ Ltd | 2024/13/45 | Invalid Date&#xa;003 | [blank] | 2024-01-15 | Missing Vendor&#xa;004 | DEF Inc | 2024-01-16 | Duplicate&#xa;&#xa;[Select All] [Export Issues]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;fontFamily=Courier;" parent="1" vertex="1">
          <mxGeometry x="70" y="790" width="500" height="200" as="geometry" />
        </mxCell>
        <mxCell id="ai-suggestions" value="AI Suggestions&#xa;&#xa;✓ Fix date format: 2024/13/45 → 2024-01-15&#xa;✓ Fill missing vendor: Use ABC Corp (90% confidence)&#xa;✓ Remove duplicate record #004&#xa;✓ Standardize vendor names (23 variations found)&#xa;&#xa;[Accept All] [Review Individual] [Reject All]&#xa;&#xa;Explain AI Logic" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e3f2fd;strokeColor=#1976d2;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;" parent="1" vertex="1">
          <mxGeometry x="590" y="790" width="300" height="200" as="geometry" />
        </mxCell>
        <mxCell id="data-actions" value="[Apply Fixes] [Download Audit Log] [Schedule Auto-Fix]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#4caf50;fontColor=#ffffff;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="70" y="1010" width="820" height="40" as="geometry" />
        </mxCell>
        <mxCell id="purchasing-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#dee2e6;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="1150" y="50" width="1050" height="740" as="geometry" />
        </mxCell>
        <mxCell id="purchasing-header" value="Purchase Intelligence Module" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#4caf50;fontColor=#ffffff;fontSize=18;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1150" y="50" width="1050" height="50" as="geometry" />
        </mxCell>
        <mxCell id="purchasing-breadcrumb" value="Home &gt; Dashboard &gt; Purchasing" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#6c757d;" parent="1" vertex="1">
          <mxGeometry x="1170" y="110" width="300" height="20" as="geometry" />
        </mxCell>
        <mxCell id="contract-selector" value="Contract Selection&#xa;&#xa;Contract ID: [CONT-2024-001] [Search]&#xa;Vendor: ABC Manufacturing Corp&#xa;Value: $2,450,000&#xa;Term: Jan 2024 - Dec 2024" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;" parent="1" vertex="1">
          <mxGeometry x="1170" y="140" width="400" height="120" as="geometry" />
        </mxCell>
        <mxCell id="contract-ai" value="AI Extracted Terms&lt;br&gt;&lt;br&gt;• Payment: Net 30 days&lt;br&gt;• Unit Price: $125.50 per widget&lt;br&gt;• Min Order: 1000 units&lt;br&gt;• Delivery: FOB Destination&lt;br&gt;• Price Lock: 6 months&lt;br&gt;&lt;br&gt;&lt;div&gt;&amp;nbsp; Explain Extraction&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;" parent="1" vertex="1">
          <mxGeometry x="1580" y="120" width="320" height="150" as="geometry" />
        </mxCell>
        <mxCell id="timeline" value="Purchase Order and Invoice Timeline&#xa;&#xa;Contract Start ——————————————————————— Contract End&#xa;Jan 2024                                                Dec 2024&#xa;&#xa;    PO-001  PO-002      PO-003        PO-004&#xa;      ↓       ↓           ↓             ↓&#xa;    INV-A   INV-B   [MISSING]     INV-D&#xa;              ⚠              ⚠&#xa;                                Price Mismatch" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;fontFamily=Courier;" parent="1" vertex="1">
          <mxGeometry x="1169" y="280" width="721" height="170" as="geometry" />
        </mxCell>
        <mxCell id="discrepancies" value="Discrepancies Detected&#xa;&#xa;Issue Type | PO/Invoice | Details | Action&#xa;Price Variance| PO-002 | $130 vs $125.50 contract | [Review]&#xa;Missing Invoice| PO-003 | Invoice overdue by 15 days | [Follow Up]&#xa;Early Payment| INV-D | Paid before delivery | [Flag]&#xa;Quantity Variance| PO-004 | 950 units vs 1000 min | [Escalate]&#xa;&#xa;[Approve All] [Bulk Actions] [Export Report]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ff9800;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;fontFamily=Courier;" parent="1" vertex="1">
          <mxGeometry x="1169" y="460" width="720" height="180" as="geometry" />
        </mxCell>
        <mxCell id="comments" value="Comments and Actions&#xa;&#xa;[Add Comment] [Assign Reviewer] [Set Priority]&#xa;&#xa;Latest: Price increase approved by procurement - J.Smith&#xa;Status: Under Review" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;" parent="1" vertex="1">
          <mxGeometry x="1170" y="650" width="730" height="110" as="geometry" />
        </mxCell>
        <mxCell id="bom-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#dee2e6;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="1150" y="770" width="1080" height="790" as="geometry" />
        </mxCell>
        <mxCell id="bom-header" value="BOM Comparison Tool" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ff9800;fontColor=#ffffff;fontSize=18;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1150" y="800" width="1050" height="50" as="geometry" />
        </mxCell>
        <mxCell id="bom-breadcrumb" value="Home &gt; Dashboard &gt; BOM Review" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#6c757d;" parent="1" vertex="1">
          <mxGeometry x="1170" y="860" width="300" height="20" as="geometry" />
        </mxCell>
        <mxCell id="product-selector" value="Product Selection&#xa;&#xa;Product ID: [PROD-WGT-001] [Search]&#xa;Product Name: Advanced Widget Assembly&#xa;&#xa;Compare: [Engineering BOM v2.1] vs [Production BOM v2.0]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;" parent="1" vertex="1">
          <mxGeometry x="1170" y="890" width="720" height="80" as="geometry" />
        </mxCell>
        <mxCell id="bom-left" value="Engineering BOM v2.1&#xa;&#xa;Part # | Description | Qty | Unit&#xa;WGT-001| Base Frame | 1 | EA&#xa;WGT-002| Motor Assembly| 1 | EA&#xa;WGT-003| Control Board | 2 | EA | WARNING&#xa;WGT-004| Cable Harness | 1 | EA&#xa;WGT-005| Mounting Bracket| 4 | EA&#xa;WGT-NEW| Safety Switch | 1 | EA | WARNING" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;fontFamily=Courier;" parent="1" vertex="1">
          <mxGeometry x="1169" y="1000" width="350" height="200" as="geometry" />
        </mxCell>
        <mxCell id="bom-right" value="Production BOM v2.0&#xa;&#xa;Part # | Description | Qty | Unit&#xa;WGT-001| Base Frame | 1 | EA&#xa;WGT-002| Motor Assembly| 1 | EA&#xa;WGT-003| Control Board | 1 | EA | WARNING&#xa;WGT-004| Cable Harness | 1 | EA&#xa;WGT-005| Mounting Bracket| 4 | EA&#xa;WGT-OLD| Legacy Switch | 1 | EA | WARNING" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;fontFamily=Courier;" parent="1" vertex="1">
          <mxGeometry x="1540" y="1000" width="350" height="200" as="geometry" />
        </mxCell>
        <mxCell id="bom-ai" value="AI Discrepancy Analysis&#xa;&#xa;Found 3 discrepancies:&#xa;&#xa;1. Quantity Mismatch:&#xa;   WGT-003 Control Board: 2 vs 1&#xa;   Recommendation: Update production to qty 2&#xa;&#xa;2. Missing Part:&#xa;   WGT-NEW Safety Switch not in production BOM&#xa;   Recommendation: Add to production (safety requirement)&#xa;&#xa;3. Obsolete Part:&#xa;   WGT-OLD Legacy Switch superseded by WGT-NEW&#xa;   Recommendation: Phase out legacy part&#xa;&#xa;[Accept All] [Review Individual] [Forward to Engineering]&#xa;&#xa;Explain Analysis" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ff9800;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;" parent="1" vertex="1">
          <mxGeometry x="1170" y="1210" width="720" height="310" as="geometry" />
        </mxCell>
        <mxCell id="oee-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#dee2e6;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="50" y="1250" width="1050" height="600" as="geometry" />
        </mxCell>
        <mxCell id="oee-header" value="OEE Monitoring Dashboard" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e91e63;fontColor=#ffffff;fontSize=18;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="50" y="1250" width="1050" height="50" as="geometry" />
        </mxCell>
        <mxCell id="oee-breadcrumb" value="Home &gt; Dashboard &gt; OEE Monitoring" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#6c757d;" parent="1" vertex="1">
          <mxGeometry x="70" y="1310" width="300" height="20" as="geometry" />
        </mxCell>
        <mxCell id="oee-availability" value="Availability&#xa;&#xa;92.5%&#xa;Target: 95%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;align=center;verticalAlign=middle;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="70" y="1340" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="oee-performance" value="Performance&#xa;&#xa;84.2%&#xa;Target: 90%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff3e0;strokeColor=#ff9800;align=center;verticalAlign=middle;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="210" y="1340" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="oee-quality" value="Quality&#xa;&#xa;98.1%&#xa;Target: 98%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e8f5e8;strokeColor=#4caf50;align=center;verticalAlign=middle;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="350" y="1340" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="oee-overall" value="Overall OEE&#xa;&#xa;76.5%&#xa;Target: 85%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#e91e63;align=center;verticalAlign=middle;fontSize=16;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="490" y="1340" width="140" height="80" as="geometry" />
        </mxCell>
        <mxCell id="data-source" value="Data Sources&#xa;&#xa;• IoT Sensors: Live&#xa;• MES System: Live&#xa;• Manual Entry: 2h delay&#xa;• ERP Integration: Live" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;" parent="1" vertex="1">
          <mxGeometry x="680" y="1320" width="220" height="120" as="geometry" />
        </mxCell>
        <mxCell id="oee-chart" value="OEE Trend (Last 24 Hours)&#xa;&#xa;100% |                    ⚬&#xa;  90% |        ⚬      ⚬     ⚬   ⚬&#xa;  80% |    ⚬       ⚬         ⚬&#xa;  70% | ⚬     ⚬&#xa;  60% |&#xa;       0   4   8  12  16  20  24&#xa;       Hours&#xa;&#xa;Current: 76.5% | Peak: 94.2% | Low: 68.1%" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;fontFamily=Courier;" parent="1" vertex="1">
          <mxGeometry x="70" y="1440" width="500" height="200" as="geometry" />
        </mxCell>
        <mxCell id="oee-alerts" value="AI Alerts and Root Cause Analysis&#xa;&#xa;ACTIVE ALERTS:&#xa;&#xa;1. Performance Drop Detected (14:30)&#xa;   Line 3 - Widget Assembly&#xa;   Probable Cause: Material shortage (98% confidence)&#xa;   Recommendation: Check material inventory&#xa;&#xa;2. Quality Issue Trend (13:45)&#xa;   Defect rate increased 2.3%&#xa;   Probable Cause: Tool wear on Station 5&#xa;   Recommendation: Schedule maintenance&#xa;&#xa;3. Downtime Spike (12:15 - 12:45)&#xa;   30-minute unplanned stop&#xa;   Root Cause: Late material delivery from vendor ABC&#xa;   Impact: 3.2% OEE reduction&#xa;&#xa;[Acknowledge] [Create Work Order] [Escalate]&#xa;&#xa;Explain AI Logic" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fce4ec;strokeColor=#e91e63;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;" parent="1" vertex="1">
          <mxGeometry x="580" y="1460" width="490" height="370" as="geometry" />
        </mxCell>
        <mxCell id="machine-list" value="Machine/Line Performance&#xa;&#xa;Line | Machine | OEE | Status | Last Issue&#xa;Line 1| CNC-001 | 89% | Running| Tool change&#xa;Line 2| ASM-002 | 76% | Running| Material wait&#xa;Line 3| WLD-003 | 68% | Alert  | Quality issue&#xa;Line 4| PKG-004 | 94% | Running| None&#xa;&#xa;[Filter by Plant] [Shift View] [Operator View]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;fontFamily=Courier;" parent="1" vertex="1">
          <mxGeometry x="70" y="1660" width="500" height="160" as="geometry" />
        </mxCell>
        <mxCell id="action-container" value="" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8f9fa;strokeColor=#dee2e6;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="1210" y="1590" width="900" height="500" as="geometry" />
        </mxCell>
        <mxCell id="action-header" value="Action Center and Notifications" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#6c757d;fontColor=#ffffff;fontSize=18;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1210" y="1590" width="900" height="50" as="geometry" />
        </mxCell>
        <mxCell id="action-breadcrumb" value="Home &gt; Dashboard &gt; Action Center" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontColor=#6c757d;" parent="1" vertex="1">
          <mxGeometry x="1230" y="1650" width="300" height="20" as="geometry" />
        </mxCell>
        <mxCell id="action-filters" value="Filters: [All Modules] [High Priority] [Pending] [Last 7 Days]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=middle;spacingLeft=15;" parent="1" vertex="1">
          <mxGeometry x="1230" y="1680" width="600" height="40" as="geometry" />
        </mxCell>
        <mxCell id="action-list" value="Pending Actions (23 items)&#xa;&#xa;Priority | Module | Description | Due | Action&#xa;High  | Data   | 156 date format errors | Today | [Fix Now]&#xa;Med   | Purchase| PO-002 price variance | 2 days | [Review]&#xa;Med   | BOM    | Safety switch missing | 3 days | [Update]&#xa;High  | OEE    | Line 3 quality alert | ASAP | [Investigate]&#xa;Low   | Data   | Vendor name cleanup | 1 week | [Schedule]&#xa;Med   | Purchase| Invoice INV-003 overdue | Today | [Follow Up]&#xa;Low   | BOM    | Engineering review req | 5 days | [Assign]&#xa;&#xa;[Select All] [Bulk Actions] [Export List]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffffff;strokeColor=#d6d6d6;align=left;verticalAlign=top;spacingLeft=15;spacingTop=15;fontFamily=Courier;" parent="1" vertex="1">
          <mxGeometry x="1230" y="1740" width="860" height="220" as="geometry" />
        </mxCell>
        <mxCell id="quick-actions" value="Quick Actions&#xa;&#xa;[Generate Daily Report] [Schedule Follow-ups] [Escalate High Priority]&#xa;[Mark All as Reviewed] [Export to Excel] [Send Summary Email]" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#007bff;fontColor=#ffffff;align=center;verticalAlign=middle;fontSize=14;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1230" y="1980" width="860" height="80" as="geometry" />
        </mxCell>
        <mxCell id="nav1" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=3;strokeColor=#007bff;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="360" y="250" as="sourcePoint" />
            <mxPoint x="400" y="250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nav-label1" value="Login" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;fontColor=#007bff;" parent="1" vertex="1">
          <mxGeometry x="350" y="220" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="nav2" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#1976d2;curved=1;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="505" y="270" as="sourcePoint" />
            <mxPoint x="575" y="580" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nav3" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#4caf50;curved=1;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="675" y="270" as="sourcePoint" />
            <mxPoint x="1150" y="400" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nav4" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#ff9800;curved=1;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="505" y="410" as="sourcePoint" />
            <mxPoint x="1150" y="1100" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="nav5" value="" style="endArrow=classic;html=1;rounded=0;strokeWidth=2;strokeColor=#e91e63;curved=1;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="675" y="410" as="sourcePoint" />
            <mxPoint x="560" y="1250" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="help1" value="?" style="ellipse;whiteSpace=wrap;html=1;fillColor=#17a2b8;fontColor=#ffffff;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="870" y="790" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="help2" value="?" style="ellipse;whiteSpace=wrap;html=1;fillColor=#17a2b8;fontColor=#ffffff;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1870" y="140" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="help3" value="?" style="ellipse;whiteSpace=wrap;html=1;fillColor=#17a2b8;fontColor=#ffffff;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1870" y="1210" width="20" height="20" as="geometry" />
        </mxCell>
        <mxCell id="help4" value="?" style="ellipse;whiteSpace=wrap;html=1;fillColor=#17a2b8;fontColor=#ffffff;fontSize=12;fontStyle=1;" parent="1" vertex="1">
          <mxGeometry x="1070" y="1440" width="20" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
